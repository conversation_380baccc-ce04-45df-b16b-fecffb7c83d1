enum UserRole {
  manager('manager', 'مدير'),
  admin('admin', 'مدير'),
  superAdmin('super_admin', 'مدير عام'),
  specialist('specialist', 'أخصائي'),
  receptionist('receptionist', 'ريسبشنست');

  const UserRole(this.value, this.displayName);

  final String value;
  final String displayName;

  static UserRole fromString(String value) {
    switch (value.toLowerCase()) {
      case 'manager':
        return UserRole.manager;
      case 'admin':
        return UserRole.admin;
      case 'super_admin':
        return UserRole.superAdmin;
      case 'specialist':
        return UserRole.specialist;
      case 'receptionist':
        return UserRole.receptionist;
      default:
        return UserRole.receptionist; // افتراضي
    }
  }

  // صلاحيات كل دور
  bool get canAccessProducts => this == UserRole.manager || this == UserRole.admin || this == UserRole.superAdmin;
  bool get canAccessEmployees => this == UserRole.manager || this == UserRole.admin || this == UserRole.superAdmin;
  bool get canAccessInventory => this == UserRole.manager || this == UserRole.admin || this == UserRole.superAdmin;
  bool get canAccessPatients => this == UserRole.manager || this == UserRole.admin || this == UserRole.superAdmin;
  bool get canAccessSales => this == UserRole.manager || this == UserRole.admin || this == UserRole.superAdmin;
  bool get canAccessAppointments => true; // الجميع يمكنهم الوصول للحجوزات
  bool get canAccessSettings => true; // الجميع يمكنهم الوصول للإعدادات (محدود)
  bool get canAccessTasks => this == UserRole.specialist; // المهام للأخصائي فقط

  // صلاحيات الإعدادات
  bool get canAccessPermissionsSettings => false; // لا أحد يمكنه الوصول لصفحة الصلاحيات
  bool get canAccessAppSettings => this == UserRole.manager || this == UserRole.admin || this == UserRole.superAdmin;

  // صلاحيات الحجوزات
  bool get canEditAppointments => this == UserRole.manager || this == UserRole.admin || this == UserRole.superAdmin;
  bool get canPrintAppointments => this == UserRole.manager || this == UserRole.admin || this == UserRole.superAdmin;
  bool get canChangeAppointmentStatus => true; // الجميع يمكنهم تغيير الحالة
  bool get canViewAppointmentDetails => this == UserRole.manager || this == UserRole.admin || this == UserRole.superAdmin;

  // صلاحيات المرضى
  bool get canViewPatientPhone => this == UserRole.manager || this == UserRole.admin || this == UserRole.superAdmin;
  bool get canViewPatientEmail => this == UserRole.manager || this == UserRole.admin || this == UserRole.superAdmin;
}