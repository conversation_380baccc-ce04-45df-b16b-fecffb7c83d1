import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../network/supabase_client.dart';
import '../models/admin_user_model.dart';
import '../enums/user_role.dart';
import 'permissions_manager.dart';

class EnhancedAuthService {
  static final SupabaseClient _client = SupabaseConfig.client;
  
  // Get current user
  static User? get currentUser => _client.auth.currentUser;
  
  // Get current user ID (auth.uid)
  static String? get currentUserId => _client.auth.currentUser?.id;
  
  // Check if user is authenticated
  static bool get isAuthenticated => _client.auth.currentUser != null;
  
  // Sign in with email and password with permissions check
  static Future<Map<String, dynamic>> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      debugPrint('🔐 EnhancedAuthService: Attempting to sign in user: $email');
      
      final response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      
      if (response.user != null) {
        debugPrint('✅ EnhancedAuthService: Successfully signed in user: ${response.user!.email}');
        
        // التحقق من وجود المستخدم في جدول الأدمن
        final adminData = await _getAdminData(response.user!.id);
        
        if (adminData == null) {
          // المستخدم ليس من العاملين في المركز
          await signOut();
          return {
            'success': false,
            'error': 'unauthorized',
            'message': 'ليس لديك صلاحيات الدخول. هذا التطبيق خاص بالعاملين في المركز'
          };
        }
        
        final adminUser = AdminUserModel.fromJson(adminData);
        
        // التحقق من نشاط الحساب
        if (!adminUser.isActive) {
          await signOut();
          return {
            'success': false,
            'error': 'account_suspended',
            'message': 'حسابك موقوف. يرجى التواصل مع الإدارة'
          };
        }
        
        // تهيئة الصلاحيات
        PermissionsManager().initializePermissions(adminUser);
        
        // تحديث آخر تسجيل دخول
        await _updateLastLogin(adminUser.id);
        
        return {
          'success': true,
          'user': adminUser,
          'auth_response': response
        };
      }
      
      return {
        'success': false,
        'error': 'auth_failed',
        'message': 'فشل في تسجيل الدخول'
      };
    } catch (e) {
      debugPrint('❌ EnhancedAuthService: Error signing in: $e');
      return {
        'success': false,
        'error': 'exception',
        'message': 'حدث خطأ أثناء تسجيل الدخول: ${e.toString()}'
      };
    }
  }

  // Get admin data from database
  static Future<Map<String, dynamic>?> _getAdminData(String userId) async {
    try {
      final response = await SupabaseConfig.admins
          .select()
          .eq('id', userId)
          .maybeSingle();
      
      return response;
    } catch (e) {
      debugPrint('❌ EnhancedAuthService: Error getting admin data: $e');
      return null;
    }
  }

  // Update last login timestamp
  static Future<void> _updateLastLogin(String userId) async {
    try {
      await SupabaseConfig.admins
          .update({'last_login': DateTime.now().toIso8601String()})
          .eq('id', userId);
    } catch (e) {
      debugPrint('❌ EnhancedAuthService: Error updating last login: $e');
    }
  }
  
  // Sign out
  static Future<void> signOut() async {
    try {
      debugPrint('🔐 EnhancedAuthService: Signing out user');
      
      // مسح الصلاحيات
      PermissionsManager().clearPermissions();
      
      await _client.auth.signOut();
      debugPrint('✅ EnhancedAuthService: Successfully signed out');
    } catch (e) {
      debugPrint('❌ EnhancedAuthService: Error signing out: $e');
      rethrow;
    }
  }
  
  // Reset password
  static Future<void> resetPassword(String email) async {
    try {
      debugPrint('🔐 EnhancedAuthService: Sending password reset email to: $email');
      await _client.auth.resetPasswordForEmail(email);
      debugPrint('✅ EnhancedAuthService: Password reset email sent');
    } catch (e) {
      debugPrint('❌ EnhancedAuthService: Error sending password reset email: $e');
      rethrow;
    }
  }
  
  // Get current admin user
  static Future<AdminUserModel?> getCurrentAdminUser() async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        debugPrint('⚠️ EnhancedAuthService: No authenticated user');
        return null;
      }
      
      debugPrint('🔍 EnhancedAuthService: Loading admin profile for user: $userId');
      
      final adminData = await _getAdminData(userId);
      if (adminData == null) return null;
      
      final adminUser = AdminUserModel.fromJson(adminData);
      debugPrint('✅ EnhancedAuthService: Successfully loaded admin profile');
      
      return adminUser;
    } catch (e) {
      debugPrint('❌ EnhancedAuthService: Error loading admin profile: $e');
      return null;
    }
  }
  
  // Check if user is admin
  static Future<bool> isAdmin() async {
    try {
      final userId = currentUserId;
      if (userId == null) return false;
      
      final response = await SupabaseConfig.admins
          .select('id')
          .eq('id', userId)
          .limit(1);
      
      return response.isNotEmpty;
    } catch (e) {
      debugPrint('❌ EnhancedAuthService: Error checking admin status: $e');
      return false;
    }
  }
  
  // Get admin role
  static Future<UserRole?> getAdminRole() async {
    try {
      final userId = currentUserId;
      if (userId == null) return null;

      final response = await SupabaseConfig.admins
          .select('role')
          .eq('id', userId)
          .single();

      final roleString = response['role'] as String?;
      if (roleString == null) return null;
      
      return UserRole.fromString(roleString);
    } catch (e) {
      debugPrint('❌ EnhancedAuthService: Error getting admin role: $e');
      return null;
    }
  }

  // Listen to auth state changes
  static Stream<AuthState> get authStateChanges => _client.auth.onAuthStateChange;
  
  // Initialize permissions for current user
  static Future<bool> initializeCurrentUserPermissions() async {
    try {
      final adminUser = await getCurrentAdminUser();
      if (adminUser == null) return false;
      
      if (!adminUser.isActive) return false;
      
      PermissionsManager().initializePermissions(adminUser);
      return true;
    } catch (e) {
      debugPrint('❌ EnhancedAuthService: Error initializing permissions: $e');
      return false;
    }
  }
}