import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../data/repositories/appointments_repository.dart';
import 'appointments_event.dart';
import 'appointments_state.dart';

class AppointmentsBloc extends Bloc<AppointmentsEvent, AppointmentsState> {
  final AppointmentsRepository _appointmentsRepository;

  AppointmentsBloc({required AppointmentsRepository appointmentsRepository})
      : _appointmentsRepository = appointmentsRepository,
        super(AppointmentsInitial()) {
    on<LoadTodayAppointments>(_onLoadTodayAppointments);
    on<LoadAppointmentsByDate>(_onLoadAppointmentsByDate);
    on<LoadSpecialistAppointmentsByDate>(_onLoadSpecialistAppointmentsByDate);
    on<LoadPatientAppointments>(_onLoadPatientAppointments);
    on<LoadUpcomingAppointments>(_onLoadUpcomingAppointments);
    on<CreateAppointment>(_onCreateAppointment);
    on<UpdateAppointment>(_onUpdateAppointment);
    on<UpdateAppointmentStatus>(_onUpdateAppointmentStatus);
    on<DeleteAppointment>(_onDeleteAppointment);
    on<RefreshAppointments>(_onRefreshAppointments);
    on<ChangeSelectedDate>(_onChangeSelectedDate);
  }

  Future<void> _onLoadTodayAppointments(
    LoadTodayAppointments event,
    Emitter<AppointmentsState> emit,
  ) async {
    debugPrint('🔄 AppointmentsBloc: Starting to load today appointments...');
    emit(AppointmentsLoading());
    try {
      final today = DateTime.now();
      debugPrint('📅 AppointmentsBloc: Today date: ${today.toIso8601String().split('T')[0]}');

      debugPrint('📞 AppointmentsBloc: Calling getAppointmentsByDate...');
      final todayAppointments = await _appointmentsRepository.getAppointmentsByDate(today);
      debugPrint('📊 AppointmentsBloc: Received ${todayAppointments.length} today appointments');

      debugPrint('📞 AppointmentsBloc: Calling getUpcomingAppointments...');
      final upcomingAppointments = await _appointmentsRepository.getUpcomingAppointments();
      debugPrint('📊 AppointmentsBloc: Received ${upcomingAppointments.length} upcoming appointments');

      emit(AppointmentsLoaded(
        appointments: todayAppointments,
        todayAppointments: todayAppointments,
        upcomingAppointments: upcomingAppointments,
        selectedDate: today,
      ));
      debugPrint('✅ AppointmentsBloc: Successfully emitted AppointmentsLoaded state');
    } catch (e, stackTrace) {
      debugPrint('❌ AppointmentsBloc Error: $e');
      debugPrint('📍 AppointmentsBloc Stack trace: $stackTrace');
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onLoadAppointmentsByDate(
    LoadAppointmentsByDate event,
    Emitter<AppointmentsState> emit,
  ) async {
    emit(AppointmentsLoading());
    try {
      final appointments = await _appointmentsRepository.getAppointmentsByDate(event.date);
      final todayAppointments = await _appointmentsRepository.getAppointmentsByDate(DateTime.now());
      final upcomingAppointments = await _appointmentsRepository.getUpcomingAppointments();

      emit(AppointmentsLoaded(
        appointments: appointments,
        todayAppointments: todayAppointments,
        upcomingAppointments: upcomingAppointments,
        selectedDate: event.date,
      ));
    } catch (e) {
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onLoadSpecialistAppointmentsByDate(
    LoadSpecialistAppointmentsByDate event,
    Emitter<AppointmentsState> emit,
  ) async {
    emit(AppointmentsLoading());
    try {
      final appointments = await _appointmentsRepository.getSpecialistAppointmentsByDate(
        event.date,
        event.specialistId,
      );
      final todayAppointments = await _appointmentsRepository.getSpecialistAppointmentsByDate(
        DateTime.now(),
        event.specialistId,
      );
      final upcomingAppointments = await _appointmentsRepository.getSpecialistUpcomingAppointments(
        event.specialistId,
      );

      emit(AppointmentsLoaded(
        appointments: appointments,
        todayAppointments: todayAppointments,
        upcomingAppointments: upcomingAppointments,
        selectedDate: event.date,
      ));
    } catch (e) {
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onLoadPatientAppointments(
    LoadPatientAppointments event,
    Emitter<AppointmentsState> emit,
  ) async {
    emit(AppointmentsLoading());
    try {
      final appointments = await _appointmentsRepository.getPatientAppointments(event.patientId);
      final todayAppointments = await _appointmentsRepository.getAppointmentsByDate(DateTime.now());
      final upcomingAppointments = await _appointmentsRepository.getUpcomingAppointments();

      emit(AppointmentsLoaded(
        appointments: appointments,
        todayAppointments: todayAppointments,
        upcomingAppointments: upcomingAppointments,
        selectedDate: DateTime.now(),
      ));
    } catch (e) {
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onLoadUpcomingAppointments(
    LoadUpcomingAppointments event,
    Emitter<AppointmentsState> emit,
  ) async {
    emit(AppointmentsLoading());
    try {
      final upcomingAppointments = await _appointmentsRepository.getUpcomingAppointments();
      final todayAppointments = await _appointmentsRepository.getAppointmentsByDate(DateTime.now());

      emit(AppointmentsLoaded(
        appointments: upcomingAppointments,
        todayAppointments: todayAppointments,
        upcomingAppointments: upcomingAppointments,
        selectedDate: DateTime.now(),
      ));
    } catch (e) {
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onCreateAppointment(
    CreateAppointment event,
    Emitter<AppointmentsState> emit,
  ) async {
    try {
      final createdAppointment = await _appointmentsRepository.createAppointment(event.appointment);
      emit(AppointmentCreated(appointment: createdAppointment));

      // Refresh appointments
      add(LoadTodayAppointments());
    } catch (e) {
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onUpdateAppointment(
    UpdateAppointment event,
    Emitter<AppointmentsState> emit,
  ) async {
    try {
      final updatedAppointment = await _appointmentsRepository.updateAppointment(event.appointment);
      emit(AppointmentUpdated(appointment: updatedAppointment));

      // Refresh appointments
      add(LoadTodayAppointments());
    } catch (e) {
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onUpdateAppointmentStatus(
    UpdateAppointmentStatus event,
    Emitter<AppointmentsState> emit,
  ) async {
    debugPrint('🔄 AppointmentsBloc: Starting _onUpdateAppointmentStatus...');
    debugPrint('📋 AppointmentsBloc: Appointment ID: ${event.appointmentId}');
    debugPrint('📋 AppointmentsBloc: New Status: ${event.status}');

    try {
      debugPrint('🚀 AppointmentsBloc: Calling repository updateAppointmentStatus...');

      final updatedAppointment = await _appointmentsRepository.updateAppointmentStatus(
        event.appointmentId,
        event.status,
      );

      debugPrint('✅ AppointmentsBloc: Repository call successful');
      debugPrint('📋 AppointmentsBloc: Updated appointment: ${updatedAppointment.id}');

      emit(AppointmentStatusUpdated(appointment: updatedAppointment));
      debugPrint('✅ AppointmentsBloc: AppointmentStatusUpdated state emitted');

      // Refresh appointments
      debugPrint('🔄 AppointmentsBloc: Adding LoadTodayAppointments event...');
      add(LoadTodayAppointments());
      debugPrint('✅ AppointmentsBloc: LoadTodayAppointments event added');

    } catch (e) {
      debugPrint('❌ AppointmentsBloc: Error in _onUpdateAppointmentStatus: $e');
      debugPrint('❌ AppointmentsBloc: Error type: ${e.runtimeType}');
      debugPrint('❌ AppointmentsBloc: Stack trace: ${StackTrace.current}');

      emit(AppointmentsError(message: e.toString()));
      debugPrint('❌ AppointmentsBloc: AppointmentsError state emitted');
    }
  }

  Future<void> _onDeleteAppointment(
    DeleteAppointment event,
    Emitter<AppointmentsState> emit,
  ) async {
    try {
      await _appointmentsRepository.deleteAppointment(event.appointmentId);
      emit(AppointmentDeleted(appointmentId: event.appointmentId));

      // Refresh appointments
      add(LoadTodayAppointments());
    } catch (e) {
      emit(AppointmentsError(message: e.toString()));
    }
  }

  Future<void> _onRefreshAppointments(
    RefreshAppointments event,
    Emitter<AppointmentsState> emit,
  ) async {
    add(LoadTodayAppointments());
  }

  Future<void> _onChangeSelectedDate(
    ChangeSelectedDate event,
    Emitter<AppointmentsState> emit,
  ) async {
    add(LoadAppointmentsByDate(date: event.date));
  }
}