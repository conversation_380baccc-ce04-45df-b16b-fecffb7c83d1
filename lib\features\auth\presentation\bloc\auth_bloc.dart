import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/models/admin_model.dart';
import '../../../../core/services/auth_storage_service.dart';
import '../../../../core/services/enhanced_auth_service.dart';
import '../../data/repositories/auth_repository.dart';
import 'auth_event.dart';
import 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository _authRepository;

  AuthBloc({required AuthRepository authRepository})
    : _authRepository = authRepository,
      super(AuthInitial()) {
    on<LoginRequested>(_onLoginRequested);
    on<RegisterRequested>(_onRegisterRequested);
    on<LogoutRequested>(_onLogoutRequested);
    on<CheckAuthStatus>(_onCheckAuthStatus);
  }

  Future<void> _onLoginRequested(
    LoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());
    try {
      // استخدام EnhancedAuthService للتحقق من الصلاحيات
      final result = await EnhancedAuthService.signInWithEmailAndPassword(
        email: event.email,
        password: event.password,
      );

      if (result['success'] == true) {
        final adminUser = result['user'];
        
        // تحويل AdminUserModel إلى AdminModel للتوافق مع الكود الحالي
        final admin = AdminModel(
          id: adminUser.id,
          name: adminUser.name,
          email: adminUser.email,
          role: adminUser.role,
          employeeType: adminUser.role,
          isActive: adminUser.isActive,
          createdAt: adminUser.createdAt,
          updatedAt: adminUser.updatedAt,
        );

        // Save login state using AuthStorageService
        await AuthStorageService.saveLoginState(
          userId: admin.id,
          email: admin.email,
          name: admin.name,
        );
        
        emit(AuthAuthenticated(admin: admin));
      } else {
        // التعامل مع أخطاء الصلاحيات
        final errorType = result['error'];
        final message = result['message'];
        
        if (errorType == 'unauthorized') {
          emit(AuthError(message: 'ليس لديك صلاحيات الدخول. هذا التطبيق خاص بالعاملين ف�� المركز'));
        } else if (errorType == 'account_suspended') {
          emit(AuthError(message: 'حسابك موقوف. يرجى التواصل مع الإدارة'));
        } else {
          emit(AuthError(message: message ?? 'البريد الإلكتروني أو كلمة المرور غير صحيحة'));
        }
      }
    } catch (e) {
      // Improved error handling with user-friendly messages
      String errorMessage = _getErrorMessage(e.toString());
      emit(AuthError(message: errorMessage));
    }
  }

  Future<void> _onRegisterRequested(
    RegisterRequested event,
    Emitter<AuthState> emit,
  ) async {
    debugPrint('🔄 AuthBloc: Registration requested');
    debugPrint('📧 AuthBloc: Email: ${event.email}');
    debugPrint('👤 AuthBloc: Name: ${event.name}');

    emit(AuthLoading());
    try {
      // Check if email already exists
      debugPrint('🔍 AuthBloc: Checking if email exists...');
      final emailExists = await _authRepository.checkEmailExists(event.email);
      debugPrint('📧 AuthBloc: Email exists: $emailExists');

      if (emailExists) {
        debugPrint('❌ AuthBloc: Email already exists');
        emit(const AuthError(message: 'البريد الإلكتروني مستخدم بالفعل'));
        return;
      }

      debugPrint('🚀 AuthBloc: Starting registration...');
      final admin = await _authRepository.register(
        event.name,
        event.email,
        event.password,
      );

      debugPrint('💾 AuthBloc: Saving admin data locally...');
      await AuthStorageService.saveLoginState(
        userId: admin.id,
        email: admin.email,
        name: admin.name,
      );

      debugPrint('✅ AuthBloc: Registration completed successfully');
      emit(AuthAuthenticated(admin: admin));
    } catch (e, stackTrace) {
      debugPrint('❌ AuthBloc: Registration failed');
      debugPrint('💥 AuthBloc: Error: $e');
      debugPrint('📍 AuthBloc: Stack trace: $stackTrace');
      emit(AuthError(message: e.toString()));
    }
  }

  Future<void> _onLogoutRequested(
    LogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    try {
      // استخدام EnhancedAuthService لتسجيل الخروج ومسح الصلاحيات
      await EnhancedAuthService.signOut();
      await AuthStorageService.clearLoginState();
      emit(AuthUnauthenticated());
    } catch (e) {
      // Even if logout fails, clear local data and emit unauthenticated
      await EnhancedAuthService.signOut();
      await AuthStorageService.clearLoginState();
      emit(AuthUnauthenticated());
    }
  }

  Future<void> _onCheckAuthStatus(
    CheckAuthStatus event,
    Emitter<AuthState> emit,
  ) async {
    try {
      // Check if user is logged in using SharedPreferences
      final isLoggedIn = await AuthStorageService.isLoggedIn();

      if (!isLoggedIn) {
        emit(AuthUnauthenticated());
        return;
      }

      // محاولة تهيئة الصلاحيات للمستخدم الحالي
      final permissionsInitialized = await EnhancedAuthService.initializeCurrentUserPermissions();
      
      if (permissionsInitialized) {
        // الحصول على بيانات المستخدم من EnhancedAuthService
        final adminUser = await EnhancedAuthService.getCurrentAdminUser();
        
        if (adminUser != null && adminUser.isActive) {
          // تحويل AdminUserModel إلى AdminModel للتوافق مع الكود الحالي
          final admin = AdminModel(
            id: adminUser.id,
            name: adminUser.name,
            email: adminUser.email,
            role: adminUser.role,
            employeeType: adminUser.role,
            isActive: adminUser.isActive,
            createdAt: adminUser.createdAt,
            updatedAt: adminUser.updatedAt,
          );
          
          emit(AuthAuthenticated(admin: admin));
          return;
        }
      }

      // إذا فشلت تهيئة الصلاحيات، استخدم البيانات المحف��ظة محلياً
      final userData = await AuthStorageService.getUserData();
      final userId = userData['userId'];
      final email = userData['email'];
      final name = userData['name'];

      if (userId != null && email != null) {
        // Create admin model from stored data
        final admin = AdminModel(
          id: userId,
          email: email,
          name: name ?? 'مدير النظام',
          role: 'admin', // Default role for stored sessions
          employeeType: 'admin', // Default employee type
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        emit(AuthAuthenticated(admin: admin));
      } else {
        // Invalid stored data, clear and show unauthenticated
        await AuthStorageService.clearLoginState();
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      // If there's an error, clear stored data and show unauthenticated
      await AuthStorageService.clearLoginState();
      emit(AuthUnauthenticated());
    }
  }

  // Convert technical errors to user-friendly messages
  String _getErrorMessage(String error) {
    if (error.contains('Invalid login credentials') ||
        error.contains('بيانات الدخول غير صحيحة')) {
      return 'البريد الإلكتروني أو كلمة الم��ور غير صحيحة';
    } else if (error.contains('Email not confirmed')) {
      return 'يرجى تأكيد البريد الإلكتروني أولاً';
    } else if (error.contains('Too many requests')) {
      return 'تم تجاوز عدد المحاولات المسموح، يرجى المحاولة لاحقاً';
    } else if (error.contains('Network') || error.contains('connection')) {
      return 'خطأ في الاتصال بالإنترنت، يرجى التحقق من الاتصال';
    } else if (error.contains('timeout')) {
      return 'انتهت مهلة الاتصال، يرجى المحاولة مرة أخرى';
    } else if (error.contains('User not found')) {
      return 'المستخدم غير موجود';
    } else if (error.contains('Email already registered')) {
      return 'البريد الإلكتروني مسجل مسبقاً';
    } else {
      return 'حدث خطأ غير متوقع، يرجى المحاولة مرة أخرى';
    }
  }
}