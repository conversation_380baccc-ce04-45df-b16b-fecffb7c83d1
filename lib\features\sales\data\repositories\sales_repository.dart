import 'dart:developer' as developer;
import '../../../../core/network/supabase_client.dart';
import '../../../../core/models/sales_invoice_model.dart';
import '../../../../core/models/invoice_item_model.dart';
import '../../../../core/models/payment_model.dart';

class SalesRepository {
  // Generate unique invoice number
  Future<String> generateInvoiceNumber() async {
    try {
      final response = await SupabaseConfig.client
          .rpc('generate_invoice_number');
      return response as String;
    } catch (e) {
      developer.log('Error generating invoice number: $e', name: 'SalesRepository');
      throw Exception('فشل في توليد رقم الفاتورة: $e');
    }
  }

  // Generate unique payment number
  Future<String> generatePaymentNumber() async {
    try {
      final response = await SupabaseConfig.client
          .rpc('generate_payment_number');
      return response as String;
    } catch (e) {
      developer.log('Error generating payment number: $e', name: 'SalesRepository');
      throw Exception('فشل في توليد رقم الدفعة: $e');
    }
  }

  // Generate unique return number
  Future<String> generateReturnNumber() async {
    try {
      final response = await SupabaseConfig.client
          .rpc('generate_return_number');
      return response as String;
    } catch (e) {
      developer.log('Error generating return number: $e', name: 'SalesRepository');
      throw Exception('فشل في توليد رقم الاسترجاع: $e');
    }
  }

  // Create new invoice with items
  Future<SalesInvoiceModel> createInvoice(SalesInvoiceModel invoice, List<InvoiceItemModel> items) async {
    try {
      developer.log('Creating invoice: ${invoice.invoiceNumber}', name: 'SalesRepository');

      // Start transaction
      final invoiceResponse = await SupabaseConfig.salesInvoices
          .insert(invoice.toJson())
          .select()
          .single();

      final createdInvoice = SalesInvoiceModel.fromJson(invoiceResponse);
      developer.log('Invoice created with ID: ${createdInvoice.id}', name: 'SalesRepository');

      // Insert invoice items
      if (items.isNotEmpty) {
        final itemsData = items.map((item) => {
          ...item.toJson(),
          'invoice_id': createdInvoice.id, // Override with actual invoice ID
        }).toList();

        await SupabaseConfig.invoiceItems
            .insert(itemsData);

        developer.log('Inserted ${items.length} invoice items', name: 'SalesRepository');
      }

      // Update product stock
      for (final item in items) {
        await _updateProductStock(item.productId, -item.quantity);
      }

      // Return the created invoice with items
      return createdInvoice.copyWith(
        items: items.map((item) => item.copyWith(invoiceId: createdInvoice.id)).toList(),
      );
    } catch (e) {
      developer.log('Error creating invoice: $e', name: 'SalesRepository');
      throw Exception(_getErrorMessage(e, 'إنشاء الفاتورة'));
    }
  }

  // Get all invoices with pagination
  Future<List<SalesInvoiceModel>> getAllInvoices({
    int page = 1,
    int limit = 20,
    String? searchQuery,
    PaymentStatus? paymentStatus,
    InvoiceStatus? status,
    PaymentType? paymentType,
    DateTime? dateFilter,
  }) async {
    try {
      developer.log('Fetching invoices - Page: $page, Limit: $limit', name: 'SalesRepository');

      var query = SupabaseConfig.salesInvoices
          .select('''
            *,
            patients(id, name, phone, patient_id),
            admins!specialist_id(id, name, specialization_id),
            invoice_items(
              *,
              products(id, name, product_code)
            ),
            payments(*)
          ''');

      // Apply filters
      if (searchQuery != null && searchQuery.isNotEmpty) {
        // Use dedicated search method for better results
        return await _searchInvoices(
          searchQuery: searchQuery,
          page: page,
          limit: limit,
          paymentStatus: paymentStatus,
          status: status,
          paymentType: paymentType,
          dateFilter: dateFilter,
        );
      }

      if (paymentStatus != null) {
        query = query.eq('payment_status', paymentStatus.value);
      }

      if (status != null) {
        query = query.eq('status', status.value);
      }

      if (paymentType != null) {
        query = query.eq('payment_type', paymentType.value);
      }

      if (dateFilter != null) {
        final startOfDay = DateTime(dateFilter.year, dateFilter.month, dateFilter.day);
        final endOfDay = DateTime(dateFilter.year, dateFilter.month, dateFilter.day, 23, 59, 59);
        query = query.gte('created_at', startOfDay.toIso8601String())
                    .lte('created_at', endOfDay.toIso8601String());
      }

      final response = await query
          .order('created_at', ascending: false)
          .range((page - 1) * limit, page * limit - 1);

      final invoices = <SalesInvoiceModel>[];
      for (final item in response as List) {
        try {
          if (item is Map<String, dynamic>) {
            final invoice = SalesInvoiceModel.fromJson(item);
            invoices.add(invoice);
          } else {
            developer.log('Invalid invoice data type: ${item.runtimeType}', name: 'SalesRepository');
          }
        } catch (e, stackTrace) {
          developer.log('Error parsing invoice: $e', name: 'SalesRepository');
          developer.log('Stack trace: $stackTrace', name: 'SalesRepository');
          developer.log('Invoice data: $item', name: 'SalesRepository');
          // Skip this invoice and continue with others
          continue;
        }
      }

      developer.log('Fetched ${invoices.length} invoices', name: 'SalesRepository');
      return invoices;
    } catch (e) {
      developer.log('Error fetching invoices: $e', name: 'SalesRepository');
      throw Exception(_getErrorMessage(e, 'جلب الفواتير'));
    }
  }

  // Get all invoices for commission calculations
  Future<List<SalesInvoiceModel>> getAllInvoicesForCommissions({
    required int page,
    required int limit,
  }) async {
    try {
      developer.log('Fetching all invoices - Page: $page, Limit: $limit', name: 'SalesRepository');

      var query = SupabaseConfig.salesInvoices
          .select('''
            *,
            patients(id, name, phone, patient_id),
            admins!specialist_id(id, name, specialization_id),
            invoice_items(
              *,
              products(id, name, product_code)
            ),
            payments(*)
          ''');

      final response = await query
          .order('created_at', ascending: false)
          .range((page - 1) * limit, page * limit - 1);

      final invoices = <SalesInvoiceModel>[];
      for (final item in response as List) {
        try {
          if (item is Map<String, dynamic>) {
            final invoice = SalesInvoiceModel.fromJson(item);
            invoices.add(invoice);
          } else {
            developer.log('Invalid invoice data type: ${item.runtimeType}', name: 'SalesRepository');
          }
        } catch (e, stackTrace) {
          developer.log('Error parsing invoice: $e', name: 'SalesRepository');
          developer.log('Stack trace: $stackTrace', name: 'SalesRepository');
          developer.log('Invoice data: $item', name: 'SalesRepository');
          // Skip this invoice and continue with others
          continue;
        }
      }

      developer.log('Fetched ${invoices.length} invoices', name: 'SalesRepository');
      return invoices;
    } catch (e) {
      developer.log('Error fetching all invoices: $e', name: 'SalesRepository');
      throw Exception(_getErrorMessage(e, 'جلب الفواتير'));
    }
  }

  // Update invoice
  Future<SalesInvoiceModel> updateInvoice(
    SalesInvoiceModel invoice,
    List<InvoiceItemModel> items,
  ) async {
    try {
      developer.log('Updating invoice: ${invoice.invoiceNumber}', name: 'SalesRepository');

      // Update invoice
      await SupabaseConfig.salesInvoices
          .update(invoice.toJson())
          .eq('id', invoice.id);

      // Delete existing items
      await SupabaseConfig.invoiceItems
          .delete()
          .eq('invoice_id', invoice.id);

      // Insert new items
      final itemsData = items.map((item) => {
        ...item.toJson(),
        'invoice_id': invoice.id,
      }).toList();

      await SupabaseConfig.invoiceItems.insert(itemsData);

      developer.log('Invoice updated successfully: ${invoice.id}', name: 'SalesRepository');
      developer.log('Updated ${items.length} invoice items', name: 'SalesRepository');

      return invoice;
    } catch (e) {
      developer.log('Error updating invoice: $e', name: 'SalesRepository');
      throw Exception(_getErrorMessage(e, 'تحديث الفاتورة'));
    }
  }

  // Get invoice by ID
  Future<SalesInvoiceModel> getInvoiceById(String invoiceId) async {
    try {
      final response = await SupabaseConfig.salesInvoices
          .select('''
            *,
            patients(id, name, phone, patient_id),
            invoice_items(
              *,
              products(id, name, product_code)
            ),
            payments(*)
          ''')
          .eq('id', invoiceId)
          .single();

      return SalesInvoiceModel.fromJson(response);
    } catch (e) {
      developer.log('Error fetching invoice: $e', name: 'SalesRepository');
      throw Exception('فشل في جلب الفاتورة: $e');
    }
  }

  // Add payment to invoice
  Future<PaymentModel> addPayment(PaymentModel payment) async {
    try {
      developer.log('Adding payment: ${payment.paymentNumber}', name: 'SalesRepository');

      final response = await SupabaseConfig.payments
          .insert(payment.toJson())
          .select()
          .single();

      final createdPayment = PaymentModel.fromJson(response);

      // Update invoice payment status
      await _updateInvoicePaymentStatus(payment.invoiceId);

      return createdPayment;
    } catch (e) {
      developer.log('Error adding payment: $e', name: 'SalesRepository');
      throw Exception('فشل في إضافة الدفعة: $e');
    }
  }

  // Update product stock
  Future<void> _updateProductStock(String productId, int quantityChange) async {
    try {
      final newStock = await SupabaseConfig.client.rpc('increment_stock', params: {
        'product_id': productId,
        'quantity': quantityChange,
      });

      await SupabaseConfig.products
          .update({
            'stock': newStock,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', productId);
    } catch (e) {
      developer.log('Error updating product stock: $e', name: 'SalesRepository');
      // Don't throw here to avoid breaking the main operation
    }
  }

  // Update invoice payment status
  Future<void> _updateInvoicePaymentStatus(String invoiceId) async {
    try {
      // Get invoice with payments
      final invoice = await getInvoiceById(invoiceId);
      
      final totalPaid = invoice.payments.fold<double>(
        0.0, 
        (sum, payment) => sum + payment.amount,
      );

      PaymentStatus newStatus;
      if (totalPaid >= invoice.finalAmount) {
        newStatus = PaymentStatus.paid;
      } else if (totalPaid > 0) {
        newStatus = PaymentStatus.partial;
      } else {
        newStatus = PaymentStatus.partial; // Default to partial for new invoices
      }

      await SupabaseConfig.salesInvoices
          .update({
            'paid_amount': totalPaid,
            'remaining_amount': invoice.finalAmount - totalPaid,
            'payment_status': newStatus.value,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', invoiceId);
    } catch (e) {
      developer.log('Error updating invoice payment status: $e', name: 'SalesRepository');
    }
  }

  // Delete invoice
  Future<void> deleteInvoice(String invoiceId) async {
    try {
      developer.log('Deleting invoice: $invoiceId', name: 'SalesRepository');

      // Get invoice items to restore stock
      final invoice = await getInvoiceById(invoiceId);
      
      // Restore product stock
      for (final item in invoice.items) {
        await _updateProductStock(item.productId, item.quantity);
      }

      // Delete invoice (cascade will delete items and payments)
      await SupabaseConfig.salesInvoices
          .delete()
          .eq('id', invoiceId);

      developer.log('Invoice deleted successfully', name: 'SalesRepository');
    } catch (e) {
      developer.log('Error deleting invoice: $e', name: 'SalesRepository');
      throw Exception('فشل في حذف الفاتورة: $e');
    }
  }

  /// Search invoices with advanced search functionality
  Future<List<SalesInvoiceModel>> _searchInvoices({
    required String searchQuery,
    int page = 1,
    int limit = 20,
    PaymentStatus? paymentStatus,
    InvoiceStatus? status,
    PaymentType? paymentType,
    DateTime? dateFilter,
  }) async {
    try {
      // Search by invoice number
      List<SalesInvoiceModel> results = [];

      // Search by invoice number
      var invoiceQuery = SupabaseConfig.salesInvoices
          .select('''
            *,
            patients(id, name, phone, patient_id),
            invoice_items(
              *,
              products(id, name, product_code)
            ),
            payments(*)
          ''')
          .ilike('invoice_number', '%$searchQuery%');

      // Apply other filters
      if (paymentStatus != null) {
        invoiceQuery = invoiceQuery.eq('payment_status', paymentStatus.value);
      }
      if (status != null) {
        invoiceQuery = invoiceQuery.eq('status', status.value);
      }
      if (paymentType != null) {
        invoiceQuery = invoiceQuery.eq('payment_type', paymentType.value);
      }

      if (dateFilter != null) {
        final startOfDay = DateTime(dateFilter.year, dateFilter.month, dateFilter.day);
        final endOfDay = DateTime(dateFilter.year, dateFilter.month, dateFilter.day, 23, 59, 59);
        invoiceQuery = invoiceQuery.gte('created_at', startOfDay.toIso8601String())
                                  .lte('created_at', endOfDay.toIso8601String());
      }

      final invoiceResults = await invoiceQuery
          .order('created_at', ascending: false)
          .range((page - 1) * limit, page * limit - 1);

      for (final data in invoiceResults) {
        results.add(SalesInvoiceModel.fromJson(data));
      }

      return results;
    } catch (e) {
      developer.log('Error searching invoices: $e', name: 'SalesRepository');
      throw Exception(_getErrorMessage(e, 'البحث في الفواتير'));
    }
  }

  /// Convert technical errors to user-friendly Arabic messages
  String _getErrorMessage(dynamic error, String operation) {
    final errorString = error.toString().toLowerCase();

    // Database constraint errors
    if (errorString.contains('constraint') || errorString.contains('check')) {
      return 'خطأ في البيانات المدخلة. يرجى التحقق من صحة المعلومات.';
    }

    // Network/connection errors
    if (errorString.contains('network') || errorString.contains('connection') ||
        errorString.contains('timeout') || errorString.contains('socket')) {
      return 'مشكلة في الاتصال بالإنترنت. يرجى المحاولة مرة أخرى.';
    }

    // Authentication errors
    if (errorString.contains('auth') || errorString.contains('unauthorized') ||
        errorString.contains('permission')) {
      return 'ليس لديك صلاحية للقيام بهذا الإجراء.';
    }

    // Validation errors
    if (errorString.contains('validation') || errorString.contains('invalid')) {
      return 'البيانات المدخلة غير صحيحة. يرجى المراجعة والمحاولة مرة أخرى.';
    }

    // Duplicate errors
    if (errorString.contains('duplicate') || errorString.contains('unique')) {
      return 'هذه البيانات موجودة مسبقاً. يرجى استخدام بيانات مختلفة.';
    }

    // Not found errors
    if (errorString.contains('not found') || errorString.contains('404')) {
      return 'البيانات المطلوبة غير موجودة.';
    }

    // Server errors
    if (errorString.contains('500') || errorString.contains('server error')) {
      return 'خطأ في الخادم. يرجى المحاولة لاحقاً.';
    }

    // Default error message
    return 'حدث خطأ أثناء $operation. يرجى المحاولة مرة أخرى.';
  }
}
