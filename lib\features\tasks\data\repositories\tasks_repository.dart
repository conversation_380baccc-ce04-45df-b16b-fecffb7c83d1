import '../../../../core/network/supabase_client.dart';

class TasksRepository {
  // Get tasks for a specific employee
  Future<List<Map<String, dynamic>>> getEmployeeTasks(String employeeId) async {
    try {
      print('🔍 Loading tasks for employee: $employeeId');
      
      // أولاً، دعنا نتحقق من جميع المهام في الجدول
      final allTasksResponse = await SupabaseConfig.client
          .from('employee_tasks')
          .select('*')
          .order('created_at', ascending: false);
      
      print('📊 Total tasks in database: ${allTasksResponse.length}');
      
      // طباعة تفاصيل المهام للتحقق
      for (int i = 0; i < allTasksResponse.length && i < 3; i++) {
        final task = allTasksResponse[i];
        print('   Task $i: employee_id=${task['employee_id']}, title=${task['title']}');
      }
      
      // البحث عن المهام المكلف بها الموظف (employee_id) وليس التي أسندها (assigned_by)
      final response = await SupabaseConfig.client
          .from('employee_tasks')
          .select('*')
          .eq('employee_id', employeeId) // البحث بـ employee_id للموظف المكلف بالمهمة
          .order('created_at', ascending: false);

      print('📋 Found ${response.length} tasks for employee $employeeId');

      if (response.isEmpty) {
        return [];
      }

      // Get all unique assigned_by_ids
      final assignedByIds = response
          .where((task) => task['assigned_by'] != null) // Changed from assigned_by_id to assigned_by
          .map((task) => task['assigned_by'] as String)
          .toSet()
          .toList();

      print('👥 Found ${assignedByIds.length} unique assigners');

      // Get all admin users in one query
      Map<String, String> adminNames = {};
      if (assignedByIds.isNotEmpty) {
        try {
          final adminResponse = await SupabaseConfig.client
              .from('admin_users')
              .select('id, name')
              .inFilter('id', assignedByIds);

          for (final admin in adminResponse) {
            adminNames[admin['id']] = admin['name'];
          }
          print('✅ Loaded ${adminNames.length} admin names');
        } catch (e) {
          print('❌ Error fetching admin names: $e');
        }
      }

      // Process the response and add names
      final List<Map<String, dynamic>> processedTasks = [];
      for (final task in response) {
        final processedTask = Map<String, dynamic>.from(task);
        
        // Add assigned_by name
        if (task['assigned_by'] != null) { // Changed from assigned_by_id to assigned_by
          processedTask['assigned_by_name'] = 
              adminNames[task['assigned_by']] ?? 'غير محدد';
        } else {
          processedTask['assigned_by_name'] = 'غير محدد';
        }
        
        processedTasks.add(processedTask);
      }

      print('✅ Successfully processed ${processedTasks.length} tasks');
      return processedTasks;
    } catch (e) {
      print('❌ Error loading tasks: $e');
      throw Exception('Failed to load tasks: $e');
    }
  }

  // Update task status
  Future<void> updateTaskStatus(String taskId, String status) async {
    try {
      await SupabaseConfig.client
          .from('employee_tasks')
          .update({
            'status': status,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', taskId);
    } catch (e) {
      throw Exception('Failed to update task status: $e');
    }
  }

  // Get all tasks (for admin)
  Future<List<Map<String, dynamic>>> getAllTasks() async {
    try {
      // Get the basic task data
      final response = await SupabaseConfig.client
          .from('employee_tasks')
          .select('*')
          .order('created_at', ascending: false);

      if (response.isEmpty) {
        return [];
      }

      // Get all unique user IDs (both assigned_to and assigned_by)
      final allUserIds = <String>{};
      for (final task in response) {
        if (task['assigned_to'] != null) { // Changed from assigned_to_id to assigned_to
          allUserIds.add(task['assigned_to'] as String);
        }
        if (task['assigned_by'] != null) { // Changed from assigned_by_id to assigned_by
          allUserIds.add(task['assigned_by'] as String);
        }
      }

      // Get all admin users in one query
      Map<String, String> adminNames = {};
      if (allUserIds.isNotEmpty) {
        try {
          final adminResponse = await SupabaseConfig.client
              .from('admin_users')
              .select('id, name')
              .inFilter('id', allUserIds.toList());

          for (final admin in adminResponse) {
            adminNames[admin['id']] = admin['name'];
          }
        } catch (e) {
          print('Error fetching admin names: $e');
        }
      }

      // Process the response and add names
      final List<Map<String, dynamic>> processedTasks = [];
      for (final task in response) {
        final processedTask = Map<String, dynamic>.from(task);
        
        // Add assigned_to name
        if (task['assigned_to'] != null) { // Changed from assigned_to_id to assigned_to
          processedTask['assigned_to_name'] = 
              adminNames[task['assigned_to']] ?? 'غير محدد';
        } else {
          processedTask['assigned_to_name'] = 'غير محدد';
        }
        
        // Add assigned_by name
        if (task['assigned_by'] != null) { // Changed from assigned_by_id to assigned_by
          processedTask['assigned_by_name'] = 
              adminNames[task['assigned_by']] ?? 'غير محدد';
        } else {
          processedTask['assigned_by_name'] = 'غير محدد';
        }
        
        processedTasks.add(processedTask);
      }

      return processedTasks;
    } catch (e) {
      throw Exception('Failed to load all tasks: $e');
    }
  }
}