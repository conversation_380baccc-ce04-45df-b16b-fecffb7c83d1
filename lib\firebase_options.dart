// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAJYQ5J9l3XMC2oOtBNGsEA6-rZYEjC_nE',
    appId: '1:209990458957:web:4f1da8ba2033cf350ec604',
    messagingSenderId: '209990458957',
    projectId: 'iihc-8841e',
    authDomain: 'iihc-8841e.firebaseapp.com',
    storageBucket: 'iihc-8841e.firebasestorage.app',
    measurementId: 'G-1YHJC6P0JY',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBV_h_s_I2yrHSdCNysFTjph7dZmW2EBLo',
    appId: '1:209990458957:android:ffa23e656384c93b0ec604',
    messagingSenderId: '209990458957',
    projectId: 'iihc-8841e',
    storageBucket: 'iihc-8841e.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyANsfjzsUKt4Jcxn6-5F8NweqcFDrag9D8',
    appId: '1:209990458957:ios:fc2c83fc72ce68450ec604',
    messagingSenderId: '209990458957',
    projectId: 'iihc-8841e',
    storageBucket: 'iihc-8841e.firebasestorage.app',
    iosBundleId: 'com.iihcadmin',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyANsfjzsUKt4Jcxn6-5F8NweqcFDrag9D8',
    appId: '1:209990458957:ios:fc2c83fc72ce68450ec604',
    messagingSenderId: '209990458957',
    projectId: 'iihc-8841e',
    storageBucket: 'iihc-8841e.firebasestorage.app',
    iosBundleId: 'com.iihcadmin',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAJYQ5J9l3XMC2oOtBNGsEA6-rZYEjC_nE',
    appId: '1:209990458957:web:646a9b12f27f87f70ec604',
    messagingSenderId: '209990458957',
    projectId: 'iihc-8841e',
    authDomain: 'iihc-8841e.firebaseapp.com',
    storageBucket: 'iihc-8841e.firebasestorage.app',
    measurementId: 'G-GVX47XK2VK',
  );
}
